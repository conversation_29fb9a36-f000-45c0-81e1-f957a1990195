import { But<PERSON> } from "@heroui/react";
import { Add, Edit, Trash } from "iconsax-reactjs";
import { Link, useNavigate } from "react-router";
import api from "../../api";
import PageWrapper from "../../components/layout/PageWrapper";
import ListData from "../../components/table/ListData";

const OrganizationsPage = () => {
  const navigate = useNavigate();

  const columns = [
    {
      id: "1",
      title: "نام سازمان",
      type: "user",
      field: (item) => ({
        name: item.name,
        avatar: item.image,
      }),
    },

    {
      id: "2",
      title: "فعال/غیرفعال",
      field: "active",
      type: "status",
    },
    {
      id: "3",
      title: "عملیات",
      type: "custom",
      exportable: false,
      field: (item) => (
        <div className="flex justify-center gap-4 items-center">
          <Button
            as={Link}
            to={`/organizations/${item.id}/edit`}
            isIconOnly
            color="primary"
            variant="light"
          >
            <Edit className="size-7" />
          </Button>
          <Button isIconOnly color="danger" variant="light">
            <Trash className="size-7" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <PageWrapper
      headerContent={
        <Button
          type="button"
          color="secondary"
          radius="full"
          onPress={() => navigate("/organizations/create")}
          className="px-6"
          startContent={<Add className="size-7" />}
        >
          ایجاد سازمان جدید
        </Button>
      }
    >
      <ListData
        features={{
          //   export: { show: true },
          pagination: { show: true },
          table: { color: "primary", isStriped: true, selectionMode: "none" },
          filter: {
            show: true,
            className: "flex-wrap lg:flex-nowrap gap-4",
            items: [
              {
                name: "name",
                type: "search",
                props: {
                  radius: "full",
                  placeholder: "جستجوی سازمان بر اساس نام",
                  classNames: {
                    base: "w-full",
                    inputWrapper:
                      "!bg-background hover:!bg-background-100 min-h-11 shadow-sm border border-foreground-100 hover:border-foreground-200 transition-colors",
                  },
                },
              },
              {
                name: "active",
                type: "select",
                props: {
                  label: "وضعیت",
                  radius: "full",
                  items: [
                    { label: "همه", key: "null" },
                    { label: "فعال", key: "true" },
                    { label: "غیرفعال", key: "false" },
                  ],
                  placeholder: "وضعیت",
                  labelPlacement: "outside-left",
                  defaultSelectedKeys: "null",
                  classNames: {
                    base: " w-full max-w-xs",
                    trigger:
                      "!bg-background min-h-11 hover:!bg-background-100 !outline-none hover:border-foreground-200 transition-colors shadow-sm border border-foreground-100",
                  },
                },
              },
            ],
          },
        }}
        queryKey={api.Organizations.list}
        columns={columns}
      />
    </PageWrapper>
  );
};

export default OrganizationsPage;
