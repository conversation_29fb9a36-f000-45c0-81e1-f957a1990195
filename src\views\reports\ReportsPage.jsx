import { addToast } from "@heroui/react";
import { useEffect, useMemo } from "react";
import api from "../../api";
import MultiChartCard from "../../components/charts/MultiChartCard";
import PieChartCard from "../../components/charts/PieChartCard";
import RankingChart from "../../components/charts/RankingChart";
import PageWrapper from "../../components/layout/PageWrapper";

const ReportsPage = () => {
  // Get Dashboard Data
  const { data: _data, isLoading, error } = api.Reports.detail.useQuery();
  const data = useMemo(() => _data?.data, [_data]);

  useEffect(() => {
    if (_data?.status === false || error) {
      addToast({
        title: _data?.message ? _data?.message : "مشکلی پیش آمده است",
        color: "danger",
      });
    }
  }, [error, _data]);

  return (
    <PageWrapper>
      <div className="flex flex-col gap-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <div className="col-span-1">
            <RankingChart
              data={data?.charts?.top_students || []}
              isLoading={isLoading}
            />
          </div>
          <MultiChartCard
            isLoading={isLoading}
            title={"گزارش ساعات جلسات برگزار شده"}
            data={data?.charts?.session_times || []}
            charts={[
              {
                dataKey: "value",
                color: "primary",
                title: "ساعت",
              },
            ]}
            className={"col-span-1"}
            type={"bar"}
          />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <PieChartCard
            data={data?.charts?.absence_maps || []}
            isLoading={isLoading}
            title={"نسبت حضور به غیاب زبان آموزها"}
            pies={["secondary", "primary", "danger"]}
            className={"col-span-1"}
          />

          <PieChartCard
            data={data?.charts?.active_students || []}
            isLoading={isLoading}
            title={"نسبت زبان‌آموز‌های فعال به غیر‌فعال"}
            pies={["secondary", "primary", "danger"]}
            className={"col-span-1"}
          />
        </div>
      </div>
    </PageWrapper>
  );
};

export default ReportsPage;
