import { addToast } from "@heroui/react";
import { useEffect, useMemo } from "react";
import api from "../../api";
import MultiChartCard from "../../components/charts/MultiChartCard";
import PieChartCard from "../../components/charts/PieChartCard";
import PageWrapper from "../../components/layout/PageWrapper";

const ReportsPage = () => {
  // Get Dashboard Data
  const { data: _data, isLoading, error } = api.Reports.detail.useQuery();
  const data = useMemo(() => _data?.data, [_data]);

  useEffect(() => {
    if (_data?.status === false || error) {
      addToast({
        title: _data?.message ? _data?.message : "مشکلی پیش آمده است",
        color: "danger",
      });
    }
  }, [error, _data]);
  return (
    <PageWrapper>
      <div className="items-stretch grid grid-cols-1 sm:grid-cols-4 h-full gap-4">
        <PieChartCard
          data={data?.charts?.absence_maps || []}
          isLoading={isLoading}
          title={"نسبت حضور به غیاب زبان آموزها"}
          pies={["secondary", "primary", "danger"]}
          className={"xl:col-span-1  col-span-1 sm:col-span-2"}
        />

        <PieChartCard
          data={data?.charts?.active_students || []}
          isLoading={isLoading}
          title={"نسبت زبان‌آموز‌های فعال به غیر‌فعال"}
          pies={["secondary", "primary", "danger"]}
          className={"xl:col-span-1 col-span-1 sm:col-span-2 "}
        />
        <MultiChartCard
          isLoading={isLoading}
          title={"گزارش ساعات جلسات برگزار شده"}
          data={data?.charts?.session_times || []}
          charts={[
            {
              dataKey: "value",
              color: "primary",
              title: "ساعت",
            },
          ]}
          className={"sm:col-span-4 col-span-1 xl:col-span-2"}
          type={"bar"}
        />
      </div>
    </PageWrapper>
  );
};

export default ReportsPage;
