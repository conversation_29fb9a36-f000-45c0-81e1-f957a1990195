/**
 * @typedef {object} FormCheckboxGroupProps
 * @property {string} name
 * @property {object} control
 * @property {function} renderItem
 * @property {object[]} items
 * @property {CheckboxGroupProps} [checkboxGroupProps]
 *
 * @typedef {CheckboxProps & {control: object, name: string, wrapperClassName: string}} FormCheckboxProps
 *
 * @typedef {object} TableDataProps
 * @property {object[]} columns
 * @property {ReactNode} emptyContent
 * @property {object[]} data
 * @property {boolean} isLoading
 * @property {boolean} readOnly
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='default']
 * @property {'auto' | 'fixed'} [layout='auto']
 * @property {'none' | 'sm' | 'md' | 'lg'} [radius='lg']
 * @property {'none' | 'sm' | 'md' | 'lg'} [shadow='sm']
 * @property {number} [maxTableHeight=600]
 * @property {number} [rowHeight=40]
 * @property {boolean} [isVirtualized]
 * @property {boolean} [hideHeader=false]
 * @property {boolean} [isStriped=false]
 * @property {boolean} [isCompact=false]
 * @property {boolean} [isHeaderSticky=false]
 * @property {boolean} [fullWidth=true]
 * @property {boolean} [removeWrapper=false]
 * @property {React.ComponentType<any>} [BaseComponent='div']
 * @property {ReactNode} [topContent]
 * @property {ReactNode} [bottomContent]
 * @property {'inside' | 'outside'} [topContentPlacement='inside']
 * @property {'inside' | 'outside'} [bottomContentPlacement='inside']
 * @property {boolean} [showSelectionCheckboxes]
 * @property {SortDescriptor} [sortDescriptor]
 * @property {Selection} [selectedKeys]
 * @property {Selection} [defaultSelectedKeys]
 * @property {Selection} [disabledKeys]
 * @property {boolean} [disallowEmptySelection]
 * @property {'single' | 'multiple' | 'none'} [selectionMode='none']
 * @property {'toggle' | 'replace'} [selectionBehavior='toggle']
 * @property {'selection' | 'all'} [disabledBehavior='selection']
 * @property {boolean} [allowDuplicateSelectionEvents]
 * @property {boolean} [disableAnimation=false]
 * @property {CheckboxProps} [checkboxesProps]
 * @property {Partial<Record<'base' | 'table' | 'thead' | 'tbody' | 'tfoot' | 'emptyWrapper' | 'loadingWrapper' | 'wrapper' | 'tr' | 'th' | 'td' | 'sortIcon', string>>} [classNames]
 * @property {boolean} [isKeyboardNavigationDisabled=false]
 *
 *
 * @typedef {object} PaginationDataProps
 * @property {'flat' | 'bordered' | 'light' | 'faded'} [variant='flat']
 * @property {'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'} [color='default']
 * @property {'sm' | 'md' | 'lg'} [size='md']
 * @property {'none' | 'sm' | 'md' | 'lg' | 'full'} [radius='xl']
 * @property {number} [total=1]
 * @property {number} [dotsJump=5]
 * @property {number} [initialPage=1]
 * @property {number} [page]
 * @property {number} [siblings=1]
 * @property {number} [boundaries=1]
 * @property {boolean} [loop=false]
 * @property {boolean} [isCompact=false]
 * @property {boolean} [isDisabled=false]
 * @property {boolean} [showShadow=false]
 * @property {boolean} [showControls=false]
 * @property {boolean} [disableCursorAnimation=false]
 * @property {boolean} [disableAnimation=false]
 * @property {PaginationItemProps} [renderItem]
 * @property {(page: string) => string} [getItemAriaLabel]
 * @property {Partial<Record<'base' | 'wrapper' | 'prev' | 'next' | 'item' | 'cursor' | 'forwardIcon' | 'ellipsis' | 'chevronNext', string>>} [classNames]
 *
 *
 * @typedef {object} SearchFilterItem
 * @property {'search'} type
 * @property {string} name
 * @property {SearchFilterProps} props
 *
 * @typedef {object} SelectFilterItem
 * @property {string} name
 * @property {'select'} type
 * @property {SelectFilterProps} props
 *
 * @typedef {object} DatePickerFilterItem
 * @property {string} name
 * @property {'date'} type
 * @property {DatePickerFilterProps & {isRange?: boolean}} props
 *
 * @typedef {SearchFilterItem | SelectFilterItem | DatePickerFilterItem} FilterItem
 *
 * @typedef {object} ListDataProps
 * @property {object[]} columns
 * @property {string} columns.id
 * @property {string} columns.title
 * @property {(string|function)} columns.field
 * @property {string} columns.type
 * @property {boolean} [columns.exportable=true]
 * @property {QueryKey} queryKey
 * @property {object} features
 * @property {object} [features.wrapper]
 * @property {string} [features.wrapper.className]
 * @property {object} [features.pagination]
 * @property {boolean} [features.pagination.show]
 * @property {PaginationDataProps} [features.pagination.props]
 * @property {TableDataProps} [features.table]
 * @property {object} [features.export]
 * @property {boolean} [features.export.show]
 * @property {ButtonProps} [features.export.buttonProps]
 * @property {string} [features.export.textButton]
 * @property {object} [features.filter]
 * @property {boolean} [features.filter.show]
 * @property {string} [features.filter.className]
 * @property {FilterItem[]} [features.filter.items]
 *
 * @typedef {object} ExportDataProps
 * @property {object[]} data
 * @property {object[]} columns
 * @property {QueryKey} queryKey
 * @property {string} textButton
 * @property {ButtonProps} buttonProps
 * @property {(string|object)} selectedRowKeys
 *
 * @typedef {object} FilterComp
 * @property {string} name
 * @property {string} value
 * @property {(value: string) => void} setValue
 *
 * @typedef {InputProps & FilterComp} SearchFilterProps
 *
 * @typedef {DatePickerProps & FilterComp} DatePickerFilterProps
 *
 * @typedef {SelectProps & FilterComp & {chipMode: boolean}} SelectFilterProps
 *
 * @typedef {object} SelectItemProps
 * @property {ReactNode} children
 * @property {React.Key} key
 * @property {string | ReactNode} title
 * @property {string} textValue
 * @property {string | ReactNode} description
 * @property {string | ReactNode} shortcut
 * @property {ReactNode} startContent
 * @property {ReactNode} endContent
 * @property {ListboxItemSelectedIconProps} selectedIcon
 * @property {string} href
 * @property {HTMLAttributeAnchorTarget} target
 * @property {string} rel
 * @property {boolean | string} download
 * @property {string} ping
 * @property {HTMLAttributeReferrerPolicy} referrerPolicy
 * @property {boolean} shouldHighlightOnFocus
 * @property {boolean} hideSelectedIcon
 * @property {boolean} showDivider
 * @property {boolean} isDisabled
 * @property {boolean} isSelected
 * @property {boolean} isReadOnly
 * @property {Partial<Record<"base" | "wrapper" | "title" | "description" | "shortcut" | "selectedIcon", string>>} classNames
 *
 * @typedef {object} FormUploadAvatarProps
 * @property {string} name
 * @property {object} control
 * @property {object} [classNames]
 * @property {string} [classNames.wrapper]
 * @property {string} [classNames.avatar]
 * @property {string} [classNames.button]
 *
 * @typedef {object} FormInputNumberItem
 * @property {string} name
 * @property {object} control
 * @property {object} rules
 * @property {'number'} type
 * @property {NumberInputProps} inputProps
 *
 * @typedef {object} FormInputTextItem
 * @property {string} name
 * @property {object} control
 * @property {object} rules
 * @property {'text'} type
 * @property {InputProps} inputProps
 *
 * @typedef {object} FormTextareaProps
 * @property {string} name
 * @property {object} control
 * @property {object} rules
 * @property {TextareaProps} textareaProps
 *
 * @typedef {object} FormAutoCompleteProps
 * @property {string} name
 * @property {object[]} items
 * @property {function} renderItem
 * @property {object} control
 * @property {AutoCompleteProps} autoCompleteProps
 *
 * @typedef {object} FormRadioGroupProps
 * @property {string} name
 * @property {object} control
 * @property {function} renderItem
 * @property {object[]} items
 * @property {RadioGroupProps} radioGroupProps
 *
 * @typedef {FormInputNumberItem | FormInputTextItem} FormInputProps
 *
 * @typedef {DatePickerProps & {isRange?: boolean , control: object, name: string}} FormDatePickerProps
 *
 * @typedef {object} FormRichTextProps
 * @property {string} name
 * @property {object} control
 * @property {object} [enabledButtons]
 * @property {boolean} [enabledButtons.link=true]
 * @property {boolean} [enabledButtons.bold=true]
 * @property {boolean} [enabledButtons.italic=true]
 * @property {boolean} [enabledButtons.underline=true]
 * @property {boolean} [enabledButtons.strike=true]
 * @property {boolean} [enabledButtons.heading=true]
 * @property {boolean} [enabledButtons.alignment=true]
 * @property {boolean} [enabledButtons.unorderedList=true]
 * @property {boolean} [enabledButtons.orderedList=true]
 * @property {boolean} [enabledButtons.image=true]
 * @property {boolean} [enabledButtons.link=true]
 * @property {boolean} [enabledButtons.undo=true]
 * @property {boolean} [enabledButtons.redo=true]
 * @property {string} className
 *
 */
