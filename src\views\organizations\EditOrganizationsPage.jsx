import { addToast, useDisclosure } from "@heroui/react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useCallback, useEffect, useMemo, useState } from "react";
import { Form, useForm } from "react-hook-form";
import { useParams } from "react-router";
import z from "zod";
import api from "../../api";
import PageWrapper from "../../components/layout/PageWrapper";
import ActionButtons from "./components/ActionButtons";
import CreateAdminModal from "./components/CreateAdminModal";
import OrganizationInfoForm from "./components/OrganizationInfoForm";
import PermissionsSection from "./components/PermissionsSection";
import RoleSection from "./components/RoleSection";
import UserSearchSection from "./components/UserSearchSection";

const organizationSchema = z.object({
  name: z.string().min(1, "لطفا نام سازمان را وارد کنید"),
  active: z.boolean({
    required_error: "لطفا وضعیت سازمان را وارد کنید",
  }),
  role_name: z.string().min(1, "لطفا عنوان نقش را وارد کنید"),
  role_fa_name: z.string().min(1, "لطفا نام فارسی نقش را وارد کنید"),
});

const EditOrganizationsPage = () => {
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [selectedPermissions, setSelectedPermissions] = useState([]);
  const MAX_SELECTED_USERS = 1;

  // Disclosure for create modal
  const { isOpen, onOpen, onOpenChange, onClose } = useDisclosure();

  // Get Organization data
  const { id } = useParams();

  const {
    data: _data,
    isLoading,
    error,
  } = api.Organizations.detail.useQuery({
    variables: {
      id,
    },
  });

  const data = useMemo(() => _data?.data, [_data]);
  useEffect(() => {
    if (_data?.status === false || error) {
      addToast({
        title: _data?.message ? _data?.message : "مشکلی پیش آمده است",
        color: "danger",
      });
    }
  }, [error, _data]);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(organizationSchema),
    defaultValues: {
      name: "",
      active: false,
      role_name: "",
      role_fa_name: "",
    },
  });

  const handleUsersChange = useCallback((users) => {
    setSelectedUsers(users);
  }, []);

  const handlePermissionsChange = useCallback((permissions) => {
    setSelectedPermissions(permissions);
  }, []);

  const { mutate } = api.Organizations.update.useMutation({
    onSuccess: (data) => {
      addToast({
        title: data?.message,
        variant: "solid",
        color: data?.status ? "success" : "danger",
      });
    },
    onError: () => {
      addToast({
        title: "مشکلی پیش آمده است، مجددا تلاش کنید",
        color: "danger",
      });
    },
  });

  const onSubmit = useCallback(
    (data) => {
      // Validate permissions
      if (selectedPermissions.length === 0) {
        addToast({
          title: "لطفا دسترسی ها را وارد کنید",
          color: "danger",
        });
        return;
      }

      // Validate entity_id (selected users)
      if (selectedUsers.length === 0) {
        addToast({
          title: "لطفا شناسه ادمین را وارد کنید",
          color: "danger",
        });
        return;
      }

      const permissionsString = selectedPermissions.join(",");

      const formData = {
        ...data,
        entity_id:
          MAX_SELECTED_USERS === 1
            ? selectedUsers[0]?.id
            : selectedUsers.map((user) => user.id),
        permissions: permissionsString,
      };

      mutate(formData);
    },
    [selectedPermissions, selectedUsers],
  );
  return (
    <PageWrapper hasTitle={false}>
      <Form
        className="flex flex-col gap-4"
        control={control}
        onSubmit={handleSubmit(onSubmit)}
      >
        <OrganizationInfoForm control={control} />

        <div className="flex flex-col gap-6 rounded-small bg-background px-6 pb-7 pt-4 text-sm sm:text-base md:px-7 md:pb-8 lg:px-12 lg:pb-8 lg:pt-10">
          <UserSearchSection
            onUsersChange={handleUsersChange}
            onOpenCreateModal={onOpen}
            MAX_SELECTED_USERS={MAX_SELECTED_USERS}
          />

          <CreateAdminModal
            isOpen={isOpen}
            onOpenChange={onOpenChange}
            onClose={onClose}
          />

          <RoleSection control={control} />

          <PermissionsSection onPermissionsChange={handlePermissionsChange} />

          <ActionButtons />
        </div>
      </Form>
    </PageWrapper>
  );
};

export default EditOrganizationsPage;
