import PersianDate from "@alireza-ab/persian-date";
import { Avatar, Skeleton, Tooltip, addToast } from "@heroui/react";
import { InfoCircle } from "iconsax-reactjs";
import { useCallback, useEffect, useMemo } from "react";
import { useParams } from "react-router";
import api from "../../api";
import CircularChartCard from "../../components/charts/CircularChartCard";
import MultiChartCard from "../../components/charts/MultiChartCard";
import PieChartCard from "../../components/charts/PieChartCard";
import PageWrapper from "../../components/layout/PageWrapper";

const ReportStudentPage = () => {
  const { id } = useParams();

  const {
    data: _data,
    isLoading,
    error,
  } = api.Students.detail.useQuery({
    variables: {
      id,
    },
  });
  const data = useMemo(() => _data?.data, [_data]);
  useEffect(() => {
    if (_data?.status === false || error) {
      addToast({
        title: _data?.message ? _data?.message : "مشکلی پیش آمده است",
        color: "danger",
      });
    }
  }, [error, _data]);

  useEffect(() => {
    if (data?.user) {
      document.title = `باران - ${data.user?.first_name} ${data.user?.last_name}`;
    }
  }, [data]);

  const CustomTick = useCallback(
    ({ x, y, payload, isMobile, isTablet }) => {
      if (!data?.charts?.feedback || !data.charts.feedback[payload?.index])
        return null;

      const sessionData = data?.charts?.feedback[payload?.index];
      const date = new Date(sessionData?.date);
      const persianDate = new PersianDate(date);
      const formattedDate = `(${persianDate.toString()})`;

      const textProps = isMobile
        ? {
            transform: `rotate(-90 ${x} ${y})`,
            textAnchor: "end",
            y: y,
          }
        : isTablet
          ? {
              transform: `rotate(-45 ${x} ${y})`,
              textAnchor: "end",
              y: y,
            }
          : { textAnchor: "middle", y: y + 10 };

      return (
        <text x={x} dy={16} fontSize="12" {...textProps}>
          <tspan x={x} dy="0em">
            {sessionData?.title}
          </tspan>
          <tspan x={x} dy="1.2em">
            {formattedDate}
          </tspan>
        </text>
      );
    },
    [data?.charts?.feedback],
  );

  return (
    <>
      <PageWrapper hasTitle={false}>
        <div className="flex flex-col gap-6 rounded-small bg-background px-6 pb-7 pt-4 text-sm sm:text-base md:px-7 md:pb-8 lg:px-8 lg:pb-9 lg:pt-5">
          <div className="flex flex-wrap items-center justify-between gap-2">
            {!isLoading && (
              <div className="flex items-center gap-3">
                <Avatar
                  className="size-12 shrink-0 sm:size-14"
                  src={data?.user?.avatar ?? "/images/user.png"}
                />
                <h2 className="font-medium">{`${data?.user?.first_name} ${data?.user?.last_name}`}</h2>
              </div>
            )}
            {isLoading && (
              <div className="flex items-center gap-3">
                <Skeleton className="size-10 shrink-0 rounded-full sm:size-11" />
                <Skeleton className="h-4 w-32 rounded-lg" />
              </div>
            )}
          </div>

          {/* Rows */}
          <div className="flex flex-col items-start gap-x-4 gap-y-5 sm:flex-row sm:flex-wrap">
            <div className="flex w-full shrink-0 items-center justify-between whitespace-nowrap font-medium sm:basis-1/6 sm:justify-normal sm:gap-2 md:w-fit">
              {!isLoading && (
                <>
                  <p className="text-foreground-400">جلسات برگزار شده: </p>
                  <p className="">
                    {data?.statistics?.passed_sessions === 0
                      ? "صفر جلسه"
                      : data?.statistics?.passed_sessions
                        ? `${data?.statistics?.passed_sessions} جلسه `
                        : "ـ"}
                  </p>
                </>
              )}
              {isLoading && <Skeleton className="h-4 w-full rounded-lg" />}
            </div>
            <div className="flex w-full shrink-0 items-center justify-between whitespace-nowrap font-medium sm:basis-1/6 sm:justify-normal sm:gap-2 md:w-fit">
              {!isLoading && (
                <>
                  <p className="text-foreground-400">جلسات لغو شده: </p>
                  <p className="">
                    {data?.statistics?.rejected_sessions === 0
                      ? "صفر جلسه"
                      : data?.statistics?.rejected_sessions
                        ? `${data?.statistics?.rejected_sessions} جلسه `
                        : "ـ"}
                  </p>
                </>
              )}
              {isLoading && <Skeleton className="h-4 w-full rounded-lg" />}
            </div>
            <div className="flex w-full shrink-0 items-center justify-between whitespace-nowrap font-medium sm:basis-1/6 sm:justify-normal sm:gap-2 md:w-fit">
              {!isLoading && (
                <>
                  <p className="text-foreground-400">معدل Effort: </p>
                  <div className="flex items-center gap-2">
                    <p>
                      {data?.statistics?.effort
                        ? `${data?.statistics?.effort} از 5`
                        : "_"}
                    </p>
                    <Tooltip
                      classNames={{
                        content: ["py-1.5 px-4", "text-primary", "font-medium"],
                      }}
                      showArrow
                      content="learning effort in class"
                    >
                      <InfoCircle className="size-5 mb-1 text-primary" />
                    </Tooltip>
                  </div>
                </>
              )}
              {isLoading && <Skeleton className="h-4 w-full rounded-lg" />}
            </div>
            <div className="flex w-full shrink-0 items-center justify-between whitespace-nowrap font-medium sm:basis-1/6 sm:justify-normal sm:gap-2 md:w-fit">
              {!isLoading && (
                <>
                  <p className="text-foreground-400">معدل A&R: </p>
                  <div className="flex items-center gap-2">
                    <p>
                      {data?.statistics?.attention
                        ? `${data?.statistics?.attention} از 5`
                        : "_"}
                    </p>
                    <Tooltip
                      classNames={{
                        content: ["py-1.5 px-4", "text-primary", "font-medium"],
                      }}
                      showArrow
                      content="Attention and readiness in class"
                    >
                      <InfoCircle className="size-5 mb-1 text-primary" />
                    </Tooltip>
                  </div>
                </>
              )}
              {isLoading && <Skeleton className="h-4 w-full rounded-lg" />}
            </div>
            <div className="flex w-full shrink-0 items-center justify-between whitespace-nowrap font-medium sm:basis-1/6 sm:justify-normal sm:gap-2 md:w-fit">
              {!isLoading && (
                <>
                  <p className="text-foreground-400">نام مدرس: </p>
                  <p className="">
                    {data?.teacher?.name ? data?.teacher?.name : "_"}
                  </p>
                </>
              )}
              {isLoading && <Skeleton className="h-4 w-full rounded-lg" />}
            </div>
          </div>
        </div>

        <p className="font-medium">عملکرد آموزشی</p>
        <div className="items-stretch grid grid-cols-1 sm:grid-cols-3 h-full gap-4">
          <PieChartCard
            data={data?.charts?.absence_maps || []}
            isLoading={isLoading}
            title={"نسبت حضور به غیاب زبان آموزها"}
            pies={["secondary", "primary"]}
            className={"sm:col-span-1"}
          />
          <MultiChartCard
            isLoading={isLoading}
            title={"آمار نمرات 7 جلسه گذشته"}
            data={data?.charts?.feedback || []}
            charts={[
              {
                dataKey: "attention",
                color: "primary",
                title: "A&R",
              },
              {
                dataKey: "effort",
                color: "secondary",
                title: "effort",
              },
            ]}
            className={"sm:col-span-2"}
            type={"bar"}
            CustomXTick={CustomTick}
          />
          <CircularChartCard
            className={"sm:col-span-1"}
            allLevels={data?.charts?.levels.all}
            lastUserLevel={data?.charts?.levels.last_user_level}
          />
        </div>
      </PageWrapper>
    </>
  );
};

export default ReportStudentPage;
