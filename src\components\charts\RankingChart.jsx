import { Avatar, Skeleton, cn } from "@heroui/react";
import PropTypes from "prop-types";

const RankingChart = ({ data = [], isLoading = false, className }) => {
  // ترتیب نمایش ستون‌ها بر اساس رتبه (از چپ به راست: 4, 2, 1, 3, 5)
  const columnOrder = [4, 2, 1, 3, 5];

  // ارتفاع ثابت برای هر رتبه (بر اساس طراحی)
  const rankHeights = {
    1: 280, // بلندترین (وسط)
    2: 220, // دومین بلندترین
    3: 200, // سومین بلندترین
    4: 180, // چهارمین بلندترین
    5: 160, // کوتاه‌ترین
  };

  // همه ستون‌ها سفید (bg-background)
  const rankColors = {
    1: "bg-background",
    2: "bg-background",
    3: "bg-background",
    4: "bg-background",
    5: "bg-background",
  };

  // تبدیل اعداد انگلیسی به فارسی
  const toPersianNumber = (num) => {
    const persianDigits = ["۰", "۱", "۲", "۳", "۴", "۵", "۶", "۷", "۸", "۹"];
    return num.toString().replace(/\d/g, (digit) => persianDigits[digit]);
  };

  // مرتب کردن داده‌ها بر اساس ترتیب ستون‌ها
  const sortedData = columnOrder
    .map((rank) => data.find((item) => item.rank === rank))
    .filter(Boolean);

  if (isLoading) {
    return (
      <div className={cn("flex items-end justify-center gap-2 ", className)}>
        {columnOrder.map((rank) => (
          <div key={rank} className="flex flex-col items-center">
            <Skeleton
              className="w-24 rounded-t-lg"
              style={{ height: `${rankHeights[rank]}px` }}
            />
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className={cn("flex items-end justify-center gap-1 ", className)}>
      {sortedData.map((item, index) => {
        if (!item) return null;

        const height = rankHeights[item.rank];
        const bgColor = rankColors[item.rank];

        // تعیین گوشه‌های گرد برای اولین و آخرین ستون
        const isFirst = index === 0;
        const isLast = index === sortedData.length - 1;
        const roundedClass = isFirst
          ? "rounded-br-lg"
          : isLast
            ? "rounded-bl-lg"
            : "";

        return (
          <div key={item.user_id} className="flex flex-col items-center">
            {/* ستون */}
            <div
              className={cn(
                "w-24 rounded-t-lg shadow-sm flex flex-col items-center justify-between p-3",
                bgColor,
                roundedClass,
              )}
              style={{ height: `${height}px` }}
            >
              {/* Avatar */}
              <div className="mb-2">
                <Avatar src={item.image} size="md" />
              </div>

              {/* نام کاربر */}
              <div className="text-center mb-1">
                <p className="text-xs font-medium text-foreground-900">
                  {`${item.first_name} ${item.last_name}`}
                </p>
              </div>

              {/* سطح کاربر */}
              <div className="text-center mb-2">
                <p className="text-xs text-foreground-600">{item.user_level}</p>
              </div>

              {/* رتبه */}
              <div className="mt-auto">
                <span className="text-2xl font-bold text-foreground-900">
                  {toPersianNumber(item.rank)}
                </span>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

RankingChart.propTypes = {
  data: PropTypes.arrayOf(
    PropTypes.shape({
      user_id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      user_level: PropTypes.string.isRequired,
      total_minutes: PropTypes.string,
      rank: PropTypes.number.isRequired,
      image: PropTypes.string,
    }),
  ),
  isLoading: PropTypes.bool,
  className: PropTypes.string,
};

export default RankingChart;
