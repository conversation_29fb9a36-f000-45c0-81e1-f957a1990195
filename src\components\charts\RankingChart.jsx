import { Avatar, Skeleton, cn } from "@heroui/react";
import PropTypes from "prop-types";

const RankingChart = ({ data = [], isLoading = false, className }) => {
  const rankHeightRatios = {
    1: 100,
    2: 80,
    3: 70,
    4: 60,
    5: 50,
  };
  const columnOrder = [4, 2, 1, 3, 5];
  const sortedData = columnOrder
    .map((rank) => data.find((item) => item.rank === rank))
    .filter(Boolean);

  return (
    <div
      className={cn(
        "flex items-end h-full min-h-80 justify-center gap-1 bg-background",
        className,
      )}
    >
      {isLoading &&
        columnOrder.map((rank) => (
          <div
            key={rank}
            className="flex flex-col items-center flex-1 h-full justify-end"
          >
            <Skeleton
              className="w-full rounded-t-lg"
              style={{ height: `${rankHeightRatios[rank]}%` }}
            />
          </div>
        ))}

      {!isLoading &&
        data &&
        sortedData.map((item, index) => {
          if (!item) return null;

          const heightRatio = rankHeightRatios[item.rank];

          return (
            <div
              key={item.user_id}
              className="flex flex-col items-center flex-1 h-full justify-end"
            >
              <div
                className={cn(
                  "w-full rounded-t-lg drop-shadow-sm flex flex-col items-center justify-start p-3 bg-background",
                  {
                    "rounded-br-lg": index === 0,
                    "rounded-bl-lg": index === sortedData.length - 1,
                  },
                )}
                style={{ height: `${heightRatio}%` }}
              >
                <Avatar src={item.image} size="lg" />

                <p className="text-sm mt-3 mb-2 font-medium line-clamp-1 text-foreground">
                  {`${item.first_name} ${item.last_name}`}
                </p>

                <p className="font-semibold text-foreground-600">
                  {item.user_level}
                </p>

                <span
                  className={cn("mt-auto text-4xl font-medium", {
                    "mb-6 text-primary text-5xl": item.rank === 1,
                    "mb-2 text-foreground-300": item.rank === 2,
                    "mb-2 text-primary-600": item.rank === 3,
                    "text-foreground-400": item.rank === 4,
                    "text-foreground-500": item.rank === 5,
                  })}
                >
                  {item.rank}
                </span>
              </div>
            </div>
          );
        })}
    </div>
  );
};

RankingChart.propTypes = {
  data: PropTypes.arrayOf(
    PropTypes.shape({
      user_id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      user_level: PropTypes.string.isRequired,
      total_minutes: PropTypes.string,
      rank: PropTypes.number.isRequired,
      image: PropTypes.string,
    }),
  ),
  isLoading: PropTypes.bool,
  className: PropTypes.string,
};

export default RankingChart;
