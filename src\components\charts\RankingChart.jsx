import { Avatar, Skeleton, cn } from "@heroui/react";
import { motion } from "framer-motion";
import PropTypes from "prop-types";

const RankingChart = ({ data = [], isLoading = false, className }) => {
  const rankHeightRatios = {
    1: 100,
    2: 80,
    3: 70,
    4: 60,
    5: 50,
  };
  const columnOrder = [4, 2, 1, 3, 5];

  // انیمیشن container
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15, // تاخیر بین ظاهر شدن هر ستون
        delayChildren: 0.1,
      },
    },
  };

  // انیمیشن هر ستون
  const columnVariants = {
    hidden: {
      y: 50, // شروع از پایین
      opacity: 0,
      scaleY: 0, // شروع با ارتفاع صفر
    },
    visible: {
      y: 0, // حرکت به موقعیت نهایی
      opacity: 1,
      scaleY: 1, // رسیدن به ارتفاع کامل
      transition: {
        type: "spring",
        damping: 25,
        stiffness: 120,
        duration: 0.6,
      },
    },
  };

  // انیمیشن محتوای داخل ستون
  const contentVariants = {
    hidden: {
      opacity: 0,
      y: 20,
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        delay: 0.3, // تاخیر تا ستون کامل ظاهر شود
        duration: 0.4,
        ease: "easeOut",
      },
    },
  };
  const sortedData = columnOrder
    .map((rank) => data.find((item) => item.rank === rank))
    .filter(Boolean);

  return (
    <motion.div
      className={cn(
        "flex items-end h-full min-h-80 justify-center gap-1 ",
        className,
      )}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {isLoading &&
        columnOrder.map((rank) => (
          <div
            key={rank}
            className="flex flex-col items-center flex-1 h-full justify-end"
          >
            <Skeleton
              className="w-full rounded-t-lg"
              style={{ height: `${rankHeightRatios[rank]}%` }}
            />
          </div>
        ))}

      {!isLoading &&
        data &&
        sortedData.map((item, index) => {
          if (!item) return null;

          const heightRatio = rankHeightRatios[item.rank];

          return (
            <motion.div
              key={item.user_id}
              className="flex flex-col items-center flex-1 h-full justify-end"
              variants={columnVariants}
            >
              <motion.div
                className={cn(
                  "w-full rounded-t-lg drop-shadow-sm flex flex-col items-center justify-start p-3 bg-background",
                  {
                    "rounded-br-lg": index === 0,
                    "rounded-bl-lg": index === sortedData.length - 1,
                  },
                )}
                style={{
                  height: `${heightRatio}%`,
                  transformOrigin: "bottom", // انیمیشن از پایین شروع شود
                }}
              >
                <motion.div
                  variants={contentVariants}
                  className="flex flex-col items-center h-full"
                >
                  <Avatar src={item.image} size="lg" />

                  <p className="text-sm mt-3 mb-2 font-medium line-clamp-1 text-foreground">
                    {`${item.first_name} ${item.last_name}`}
                  </p>

                  <p className="font-semibold text-foreground-600">
                    {item.user_level}
                  </p>

                  <span
                    className={cn("mt-auto text-4xl font-medium", {
                      "mb-6 text-primary text-5xl": item.rank === 1,
                      "mb-2 text-foreground-300": item.rank === 2,
                      "mb-2 text-primary-600": item.rank === 3,
                      "text-foreground-400": item.rank === 4,
                      "text-foreground-500": item.rank === 5,
                    })}
                  >
                    {item.rank}
                  </span>
                </motion.div>
              </motion.div>
            </motion.div>
          );
        })}
    </motion.div>
  );
};

RankingChart.propTypes = {
  data: PropTypes.arrayOf(
    PropTypes.shape({
      user_id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      user_level: PropTypes.string.isRequired,
      total_minutes: PropTypes.string,
      rank: PropTypes.number.isRequired,
      image: PropTypes.string,
    }),
  ),
  isLoading: PropTypes.bool,
  className: PropTypes.string,
};

export default RankingChart;
