import { Avatar, Skeleton, cn } from "@heroui/react";
import PropTypes from "prop-types";

const RankingChart = ({ data = [], isLoading = false, className }) => {
  // ترتیب نمایش ستون‌ها بر اساس رتبه (از چپ به راست: 5, 3, 1, 2, 4)
  const columnOrder = [5, 3, 1, 2, 4];

  // ارتفاع ثابت برای هر رتبه (بر اساس طراحی)
  const rankHeights = {
    1: 200, // بلندترین (وسط)
    2: 160, // دومین بلندترین
    3: 140, // سومین بلندترین
    4: 120, // چهارمین بلندترین
    5: 100, // کوتاه‌ترین
  };

  // رنگ‌های مختلف برای رتبه‌ها
  const rankColors = {
    1: "bg-gradient-to-t from-yellow-400 to-yellow-300", // طلایی
    2: "bg-gradient-to-t from-gray-400 to-gray-300", // نقره‌ای
    3: "bg-gradient-to-t from-orange-400 to-orange-300", // برنزی
    4: "bg-background",
    5: "bg-background",
  };

  // تبدیل اعداد انگلیسی به فارسی
  const toPersianNumber = (num) => {
    const persianDigits = ["۰", "۱", "۲", "۳", "۴", "۵", "۶", "۷", "۸", "۹"];
    return num.toString().replace(/\d/g, (digit) => persianDigits[digit]);
  };

  // مرتب کردن داده‌ها بر اساس ترتیب ستون‌ها
  const sortedData = columnOrder
    .map((rank) => data.find((item) => item.rank === rank))
    .filter(Boolean);

  if (isLoading) {
    return (
      <div className={cn("flex items-end justify-center gap-4 p-6", className)}>
        {columnOrder.map((rank) => (
          <div key={rank} className="flex flex-col items-center gap-2">
            <Skeleton className="w-16 h-16 rounded-full" />
            <Skeleton className="w-20 h-4 rounded" />
            <Skeleton className="w-16 h-3 rounded" />
            <Skeleton
              className="w-20 rounded-t-lg"
              style={{ height: `${rankHeights[rank]}px` }}
            />
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className={cn("flex items-end justify-center gap-4 p-6", className)}>
      {sortedData.map((item) => {
        if (!item) return null;

        const height = rankHeights[item.rank];
        const bgColor = rankColors[item.rank];

        return (
          <div key={item.user_id} className="flex flex-col items-center">
            {/* Avatar */}
            <div className="mb-3">
              <Avatar
                src={item.image}
                name={`${item.first_name} ${item.last_name}`}
                size="lg"
                className="border-2 border-white shadow-lg"
              />
            </div>

            {/* نام کاربر */}
            <div className="text-center mb-1">
              <p className="text-sm font-medium text-foreground-900">
                {`${item.first_name} ${item.last_name}`}
              </p>
            </div>

            {/* سطح کاربر */}
            <div className="text-center mb-3">
              <p className="text-xs text-foreground-600">{item.user_level}</p>
            </div>

            {/* ستون */}
            <div
              className={cn(
                "w-20 rounded-t-lg shadow-md flex items-end justify-center pb-2",
                bgColor,
              )}
              style={{ height: `${height}px` }}
            >
              {/* رتبه */}
              <span className="text-2xl font-bold text-foreground-900">
                {toPersianNumber(item.rank)}
              </span>
            </div>
          </div>
        );
      })}
    </div>
  );
};

RankingChart.propTypes = {
  data: PropTypes.arrayOf(
    PropTypes.shape({
      user_id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      user_level: PropTypes.string.isRequired,
      total_minutes: PropTypes.string,
      rank: PropTypes.number.isRequired,
      image: PropTypes.string,
    }),
  ),
  isLoading: PropTypes.bool,
  className: PropTypes.string,
};

export default RankingChart;
