import { Avatar, Skeleton, cn } from "@heroui/react";
import PropTypes from "prop-types";

const RankingChart = ({ data = [], isLoading = false, className }) => {
  // ترتیب نمایش ستون‌ها بر اساس رتبه (از چپ به راست: 4, 2, 1, 3, 5)
  const columnOrder = [4, 2, 1, 3, 5];

  // نسبت ارتفاع برای هر رتبه (responsive)
  const rankHeightRatios = {
    1: 100, // بلندترین (وسط) - 100%
    2: 80, // دومین بلندترین - 80%
    3: 70, // سومین بلندترین - 70%
    4: 60, // چهارمین بلندترین - 60%
    5: 50, // کوتاه‌ترین - 50%
  };

  // تبدیل اعداد انگلیسی به فارسی
  const toPersianNumber = (num) => {
    const persianDigits = ["۰", "۱", "۲", "۳", "۴", "۵", "۶", "۷", "۸", "۹"];
    return num.toString().replace(/\d/g, (digit) => persianDigits[digit]);
  };

  // مرتب کردن داده‌ها بر اساس ترتیب ستون‌ها
  const sortedData = columnOrder
    .map((rank) => data.find((item) => item.rank === rank))
    .filter(Boolean);

  if (isLoading) {
    return (
      <div
        className={cn("flex items-end h-full justify-center gap-1", className)}
      >
        {columnOrder.map((rank) => (
          <div key={rank} className="flex flex-col items-center flex-1">
            <Skeleton
              className="w-full rounded-t-lg"
              style={{ height: `${rankHeightRatios[rank]}%` }}
            />
          </div>
        ))}
      </div>
    );
  }

  return (
    <div
      className={cn("flex items-end  h-full justify-center gap-1 ", className)}
    >
      {sortedData.map((item, index) => {
        if (!item) return null;

        const heightRatio = rankHeightRatios[item.rank];

        // تعیین گوشه‌های گرد برای اولین و آخرین ستون
        const isFirst = index === 0;
        const isLast = index === sortedData.length - 1;
        const roundedClass = isFirst
          ? "rounded-br-lg"
          : isLast
            ? "rounded-bl-lg"
            : "";

        return (
          <div
            key={item.user_id}
            className="flex flex-col items-center flex-1 h-full"
          >
            {/* ستون */}
            <div
              className={cn(
                "w-full rounded-t-lg shadow-sm flex flex-col items-center justify-between p-3 bg-background",
                roundedClass,
              )}
              style={{ height: `${heightRatio}%` }}
            >
              {/* Avatar */}
              <div className="mb-2">
                <Avatar src={item.image} size="md" />
              </div>

              {/* نام کاربر */}
              <div className="text-center mb-1">
                <p className="text-xs font-medium text-foreground-900">
                  {`${item.first_name} ${item.last_name}`}
                </p>
              </div>

              {/* سطح کاربر */}
              <div className="text-center mb-2">
                <p className="text-xs text-foreground-600">{item.user_level}</p>
              </div>

              {/* رتبه */}
              <div className="mt-auto">
                <span className="text-2xl font-bold text-foreground-900">
                  {toPersianNumber(item.rank)}
                </span>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

RankingChart.propTypes = {
  data: PropTypes.arrayOf(
    PropTypes.shape({
      user_id: PropTypes.number.isRequired,
      first_name: PropTypes.string.isRequired,
      last_name: PropTypes.string.isRequired,
      user_level: PropTypes.string.isRequired,
      total_minutes: PropTypes.string,
      rank: PropTypes.number.isRequired,
      image: PropTypes.string,
    }),
  ),
  isLoading: PropTypes.bool,
  className: PropTypes.string,
};

export default RankingChart;
