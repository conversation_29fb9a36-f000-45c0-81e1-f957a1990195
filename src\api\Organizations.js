import { router } from "react-query-kit";
import createFetcher from "../utils/createFetcher";

const Organizations = router("organizations", {
  list: router.query({
    fetcher: createFetcher("/v3/organizations"),
  }),
  add: router.mutation({
    mutationFn: createFetcher("/v3/organizations", "POST"),
  }),
  update: router.mutation({
    mutationFn: createFetcher("/v3/organizations/:id", "PATCH"),
  }),
  detail: router.query({
    fetcher: createFetcher("/v3/organizations/:id"),
  }),
});

export default Organizations;
