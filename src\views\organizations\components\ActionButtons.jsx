import { But<PERSON> } from "@heroui/react";
import PropTypes from "prop-types";
import { <PERSON> } from "react-router";

const ActionButtons = ({ onCancel, onSubmit }) => {
  return (
    <div className="flex gap-3 justify-end items-center">
      <Button
        type="button"
        radius="full"
        as={Link}
        className="md:max-w-52"
        fullWidth
        size="lg"
        onPress={onCancel}
        to="/organizations"
      >
        انصراف
      </Button>
      <Button
        type="submit"
        color="primary"
        radius="full"
        className="md:max-w-52"
        fullWidth
        size="lg"
        {...(onSubmit && { onPress: onSubmit })}
      >
        ثبت سازمان
      </Button>
    </div>
  );
};

ActionButtons.propTypes = {
  onCancel: PropTypes.func.isRequired,
  onSubmit: PropTypes.func,
};

export default ActionButtons;
